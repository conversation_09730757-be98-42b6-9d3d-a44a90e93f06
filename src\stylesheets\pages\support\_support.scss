@use "../../abstracts/" as *;
@use "sass:color";

// AI Chat Container Styles
.ai-chat-container {
  border: none;
  border-radius: 1rem;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    #ffffff,
    rgba(#00b894, 0.05)
  ); // Gradient trắng đến xanh lá nhạt
  box-shadow: 0 0.5rem 1.5rem rgba(#212529, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(#00b894, 0.2);
  }

  .card-header {
    background: linear-gradient(
      90deg,
      #2ecc71,
      #00b894
    ); // Gradient từ xanh lá tươi sáng đến xanh lá nhạt
    padding: 1rem 1.5rem;
    border-bottom: none;

    h5 {
      font-family: "Quicksand", sans-serif;
      font-size: 1.4rem;
      color: #ffffff;
      text-shadow: 0 1px 2px rgba(#212529, 0.2);
    }

    .ai-avatar-header {
      width: 45px;
      height: 45px;
      border: 3px solid rgba(255, 255, 255, 0.7);
      box-shadow: 0 0 0.5rem rgba(#212529, 0.2);
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  .ai-chat-area {
    height: 400px;
    overflow-y: auto;
    padding: 1.5rem;
    background: #f1f3f5; // Xám nhạt
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f3f5;
    }

    &::-webkit-scrollbar-thumb {
      background: #ced4da;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #adb5bd;
    }
  }

  .message-bubble {
    padding: 0.75rem 1.25rem;
    border-radius: 1.25rem;
    max-width: 75%;
    word-wrap: break-word;
    font-family: "Open Sans", sans-serif;
    font-size: 0.95rem;
    position: relative;
    box-shadow: 0 2px 5px rgba(#212529, 0.1);

    // Formatted content styling
    .message-content {
      line-height: 1.6;
      opacity: 0;
      transform: translateY(10px);
      animation: messageAppear 0.5s ease-out forwards;

      // Message headers
      .message-header {
        color: #2ecc71;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 1rem 0 0.5rem 0;
        padding-bottom: 0.3rem;
        border-bottom: 2px solid rgba(#2ecc71, 0.2);

        &:first-child {
          margin-top: 0;
        }
      }

      // Highlight text (thay thế bold)
      .highlight-text {
        background: linear-gradient(120deg, rgba(#2ecc71, 0.2) 0%, rgba(#2ecc71, 0.1) 100%);
        color: #2c3e50;
        font-weight: 600;
        padding: 0.1rem 0.3rem;
        border-radius: 0.25rem;
        border-left: 3px solid #2ecc71;
      }

      // Inline code
      .inline-code {
        background: #f1f3f4;
        color: #d73a49;
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        border: 1px solid #e1e4e8;
      }

      // Important notes
      .important-note {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        border: 1px solid #ffc107;
        border-left: 4px solid #ffc107;
        padding: 0.75rem;
        margin: 0.8rem 0;
        border-radius: 0.5rem;

        i {
          color: #856404;
          margin-right: 0.5rem;
        }
      }

      // Tip boxes
      .tip-box {
        background: linear-gradient(135deg, #d1ecf1, #bee5eb);
        border: 1px solid #17a2b8;
        border-left: 4px solid #17a2b8;
        padding: 0.75rem;
        margin: 0.8rem 0;
        border-radius: 0.5rem;

        i {
          color: #0c5460;
          margin-right: 0.5rem;
        }
      }

      // Lists styling
      .formatted-list {
        margin: 0.8rem 0;
        padding-left: 1.5rem;

        &.numbered-list {
          list-style-type: decimal;

          li {
            &::marker {
              color: #2ecc71;
              font-weight: bold;
            }
          }
        }

        &:not(.numbered-list) {
          list-style-type: none;

          li {
            position: relative;

            &:before {
              content: "●";
              color: #2ecc71;
              font-weight: bold;
              position: absolute;
              left: -1.2rem;
            }
          }
        }

        li {
          margin-bottom: 0.5rem;
          padding-left: 0.3rem;
          line-height: 1.5;

          // Nested content in list items
          .highlight-text {
            font-size: 0.95em;
          }
        }
      }

      // Bold text (fallback)
      strong {
        font-weight: 600;
        color: #2c3e50;
      }

      // Italic text
      em {
        font-style: italic;
        color: #34495e;
      }

      // Paragraphs
      p {
        margin-bottom: 0.8rem;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }

        &:first-child {
          margin-top: 0;
        }
      }

      // Line breaks
      br {
        line-height: 1.8;
      }
    }

    // Animation for message appearance
    &.message-appear .message-content {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .ai-message {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;

    // AI Avatar
    .ai-avatar {
      position: relative;
      flex-shrink: 0;
      margin-top: 0.25rem;

      .ai-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #2ecc71, #27ae60);
        border: 2px solid #2ecc71;
        box-shadow: 0 2px 8px rgba(#2ecc71, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;

        i {
          color: white;
          font-size: 1.2rem;
        }

        &:hover {
          transform: scale(1.1);
        }
      }

      .ai-status-indicator {
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 12px;
        height: 12px;
        background: #2ecc71;
        border: 2px solid white;
        border-radius: 50%;
        animation: pulse-status 2s infinite;
      }
    }

    .message-bubble.ai {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      color: #212529;
      border-bottom-left-radius: 0.25rem;
      border: 1px solid rgba(#2ecc71, 0.1);
      box-shadow: 0 4px 12px rgba(#212529, 0.1);

      &:before {
        content: "";
        position: absolute;
        left: -8px;
        bottom: 0;
        width: 0;
        height: 0;
        border-bottom: 10px solid #f8f9fa;
        border-left: 10px solid transparent;
      }
    }

    .message-time {
      text-align: left;
      color: #6c757d !important;
    }
  }

  @keyframes pulse-status {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }

  .user-message {
    display: flex;
    justify-content: flex-end;

    .message-bubble.user {
      background: #2ecc71; // Xanh lá tươi sáng
      color: #ffffff;
      border-bottom-right-radius: 0.25rem;

      &:before {
        content: "";
        position: absolute;
        right: -8px;
        bottom: 0;
        width: 0;
        height: 0;
        border-bottom: 10px solid #2ecc71;
        border-right: 10px solid transparent;
      }
    }

    .message-time {
      color: rgba(255, 255, 255, 0.7) !important;
    }
  }

  .message-time {
    display: block;
    font-size: 0.75rem !important;
    margin-top: 0.25rem;
    text-align: right;
  }

  // Loading message styles
  .loading-message {
    .message-bubble.ai {
      background: #e9ecef;
      color: #6c757d;

      .typing-indicator {
        display: flex;
        align-items: center;
        gap: 0.3rem;

        .typing-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #6c757d;
          animation: typing-animation 1.4s infinite ease-in-out;

          &:nth-child(1) { animation-delay: 0s; }
          &:nth-child(2) { animation-delay: 0.2s; }
          &:nth-child(3) { animation-delay: 0.4s; }
        }
      }

      i.bi-three-dots {
        animation: loading-dots 1.5s infinite;
        font-size: 1.2rem;
      }
    }
  }

  @keyframes typing-animation {
    0%, 60%, 100% {
      transform: translateY(0);
      opacity: 0.4;
    }
    30% {
      transform: translateY(-10px);
      opacity: 1;
    }
  }

  @keyframes loading-dots {
    0%, 20% {
      opacity: 0.2;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.2;
    }
  }

  @keyframes messageAppear {
    0% {
      opacity: 0;
      transform: translateY(15px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .ai-chat-input-area {
    padding: 1rem 1.5rem;
    background: #ffffff;

    .form-control-lg {
      font-family: "Open Sans", sans-serif;
      font-size: 1rem;
      border-radius: 0.5rem;
      border: 1px solid #ced4da;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;

      &:focus {
        border-color: #2ecc71;
        box-shadow: 0 0 0 0.25rem rgba(#2ecc71, 0.2);
        outline: none;
      }

      &::placeholder {
        color: #7f8c8d;
        opacity: 0.8;
      }
    }

    .btn {
      padding: 0.5rem 1rem;
      border-radius: 0.5rem;
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      i {
        font-size: 1.25rem;
      }

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 0 0.5rem rgba(#212529, 0.2);
      }
    }

    .btn-primary {
      background: #2ecc71;
      border: none;

      &:hover {
        background: #27ae60; // Xanh đậm hơn
      }
    }

    .btn-cta-orange {
      background: #f1c40f; // Vàng nhạt
      border: none;

      &:hover {
        background: #e2b60e; // Vàng đậm hơn
      }
    }
  }
}

#ai-feedback-cta {
  background: linear-gradient(
    135deg,
    #ffffff,
    rgba(#00b894, 0.05)
  ); // Gradient trắng đến xanh lá nhạt
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 0.25rem 0.75rem rgba(#212529, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1.5rem rgba(#00b894, 0.2);
  }

  .lead {
    font-family: "Open Sans", sans-serif;
    font-size: 1.25rem;
    color: #212529;
    margin-bottom: 1rem;
  }

  .btn-outline-primary {
    font-family: "Open Sans", sans-serif;
    font-weight: 600;
    color: #2ecc71; // Xanh lá tươi sáng
    border-color: #2ecc71;
    border-radius: 0.5rem;
    padding: 0.5rem 1.5rem;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease;

    i {
      color: #2ecc71;
    }

    &:hover {
      background-color: #2ecc71;
      color: #ffffff;
      transform: scale(1.05);

      i {
        color: #ffffff;
      }
    }
  }
}

// Suggested Questions Styles
#suggested-questions {
  background: #f8f9fa; // Xám nhạt nhẹ
  padding: 2rem;
  border-radius: 1rem;

  .section-title-sm {
    font-family: "Quicksand", sans-serif;
    font-size: 1.6rem;
    color: #212529;
    margin-bottom: 1.5rem;

    i {
      color: #f1c40f; // Vàng nhạt
      animation: pulse 1.5s infinite;

      @keyframes pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.2);
        }
        100% {
          transform: scale(1);
        }
      }
    }
  }

  .suggested-question-btn {
    font-family: "Open Sans", sans-serif;
    font-size: 0.9rem;
    border-color: #ced4da;
    color: #495057;
    border-radius: 1.5rem;
    padding: 0.5rem 1.25rem;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease;
    box-shadow: 0 2px 5px rgba(#212529, 0.1);

    &:hover {
      background-color: #7f8c8d; // Xám nhạt
      color: #ffffff;
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(#212529, 0.15);
    }
  }
}

// AI Usage Guide Styles
#ai-usage-guide {
  padding: 2rem 0;
  border-top: 1px solid #dee2e6;

  .section-title-sm {
    font-family: "Quicksand", sans-serif;
    font-size: 1.6rem;
    color: #212529;
    margin-bottom: 1.5rem;

    i {
      color: #3498db;
      animation: pulse 1.5s infinite;

      @keyframes pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.2);
        }
        100% {
          transform: scale(1);
        }
      }
    }
  }

  ul {
    padding-left: 0;

    li {
      font-size: 1rem;
      color: #212529;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      margin-bottom: 1rem;

      i {
        color: #3498db;
        font-size: 1.2rem;
        margin-right: 0.75rem;
      }
    }
  }
}

// Responsive Adjustments
@media (max-width: 768px) {
  .ai-chat-container {
    .card-header {
      padding: 0.75rem 1rem;

      h5 {
        font-size: 1.2rem;
      }

      .ai-avatar-header {
        width: 35px;
        height: 35px;
      }
    }

    .ai-chat-area {
      height: 300px;
      padding: 1rem;
    }

    .message-bubble {
      font-size: 0.9rem;
      max-width: 85%;

      .message-content {
        .formatted-list {
          padding-left: 1.2rem;

          li {
            margin-bottom: 0.4rem;
            font-size: 0.9rem;
          }
        }

        strong {
          font-size: 0.9rem;
        }
      }
    }

    .ai-chat-input-area {
      padding: 0.75rem 1rem;

      .form-control-lg {
        font-size: 0.9rem;
      }

      .btn {
        padding: 0.4rem 0.8rem;

        i {
          font-size: 1rem;
        }
      }
    }
  }

  #ai-feedback-cta {
    padding: 1.5rem;

    .lead {
      font-size: 1.1rem;
    }

    .btn-outline-primary {
      padding: 0.4rem 1.25rem;
      font-size: 0.9rem;
    }
  }

  #suggested-questions {
    padding: 1.5rem;

    .section-title-sm {
      font-size: 1.4rem;
    }

    .suggested-question-btn {
      font-size: 0.85rem;
      padding: 0.4rem 1rem;
    }
  }

  #ai-usage-guide {
    padding: 1.5rem 0;

    .section-title-sm {
      font-size: 1.4rem;
    }

    ul li {
      font-size: 0.9rem;

      i {
        font-size: 1.1rem;
      }
    }
  }
}
