@use "../../abstracts/" as *;
@use "sass:color";

// Rewards page header section
.rewards-header {
  background: linear-gradient(
    135deg,
    rgba($bs-light, 0.95) 0%,
    rgba($accent-cyan, 0.02) 50%,
    rgba($bs-primary, 0.01) 100%
  );
  padding: 4rem 0;

  .rewards-badge {
    display: inline-block;
    background: linear-gradient(135deg, $bs-primary, $primary-color);
    color: $bs-light;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    font-family: $font-heading;
    font-weight: 500;
    font-size: $font-size-sm;
    letter-spacing: $letter-spacing-normal;
    margin-bottom: 1rem;

    i {
      color: $bs-warning;
    }
  }

  .section-title {
    font-family: $font-heading !important;
    color: #1e40af !important;
    font-weight: 700;
    letter-spacing: $letter-spacing-tight;
    margin-bottom: 1.5rem;

    .text-gradient {
      color: $light-green !important;
    }
  }

  .lead {
    font-family: $primary-font !important;
    color: $bs-secondary !important;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

// Points badge - Compact design
.points-badge {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(
    135deg,
    rgba($bs-warning, 0.1),
    rgba($bs-success, 0.05)
  );
  border: 2px solid rgba($bs-warning, 0.2);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  font-family: $primary-font !important;
  font-size: 1rem;
  font-weight: 600;
  color: $bs-dark;
  box-shadow: 0 4px 15px rgba($bs-warning, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba($bs-warning, 0.2);
    border-color: rgba($bs-warning, 0.3);
  }

  i {
    color: $bs-warning;
    font-size: 1.1rem;
  }

  .points-label {
    color: $bs-secondary;
    margin-right: 0.5rem;
  }

  .points-amount {
    color: $bs-warning;
    font-weight: 700;
    font-size: 1.2rem;
    margin: 0 0.25rem;
  }

  .points-unit {
    color: $bs-secondary;
    font-size: 0.9rem;
  }
}

// Categories section
.rewards-categories {
  .section-title-sm {
    font-family: $font-heading !important;
    color: #1e40af !important;
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: $letter-spacing-tight;
  }
}

// Category filters
.category-filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;

  .btn-category {
    background: linear-gradient(145deg, $bs-light, rgba($bs-light, 0.8));
    border: 2px solid rgba($bs-primary, 0.1);
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-family: $primary-font !important;
    font-weight: 600;
    color: $bs-dark;
    transition: all 0.3s ease;

    &:hover {
      border-color: $bs-primary;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba($bs-primary, 0.15);
    }

    &.active {
      background: linear-gradient(135deg, $bs-primary, $primary-color);
      border-color: $bs-primary;
      color: $bs-light;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba($bs-primary, 0.25);
    }

    i {
      margin-right: 0.5rem;
    }
  }
}

// Rewards grid
.rewards-grid {
  .reward-item {
    background: $bs-light;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba($bs-dark, 0.08);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba($bs-secondary, 0.1);
    height: 100%;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba($bs-dark, 0.12);
      border-color: rgba($bs-primary, 0.2);
    }

    .reward-image {
      position: relative;
      overflow: hidden;
      height: 220px;
      background: rgba($bs-secondary, 0.02);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.03);
      }

      .reward-badge {
        position: absolute;
        top: 1rem;
        left: 1rem;
        background: linear-gradient(135deg, $bs-success, $light-green);
        color: $bs-light;
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: 600;
        font-family: $primary-font;
      }

      .reward-cost {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba($bs-warning, 0.9);
        color: $bs-dark;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 700;
        font-family: $font-heading;
        backdrop-filter: blur(10px);

        i {
          margin-right: 0.25rem;
        }
      }
    }

    .reward-content {
      padding: 1.5rem;

      .reward-title {
        font-family: $font-heading !important;
        color: $bs-dark !important;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        letter-spacing: $letter-spacing-tight;
        line-height: 1.3;
      }

      .reward-description {
        font-family: $primary-font !important;
        color: $bs-secondary;
        font-size: 0.85rem;
        line-height: 1.5;
        margin-bottom: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .reward-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .reward-category {
          background: rgba($bs-info, 0.1);
          color: $bs-info;
          padding: 0.25rem 0.75rem;
          border-radius: 50px;
          font-size: 0.8rem;
          font-weight: 600;
          font-family: $primary-font;
        }

        .reward-stock {
          font-family: $primary-font;
          font-size: 0.85rem;
          color: $bs-secondary;

          &.low-stock {
            color: $bs-danger;
            font-weight: 600;
          }
        }
      }

      .btn-exchange {
        width: 100%;
        background: linear-gradient(135deg, $bs-success, $light-green);
        border: none;
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-family: $primary-font !important;
        font-weight: 600;
        color: $bs-light;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(
            135deg,
            color.adjust($bs-success, $lightness: -10%),
            $bs-success
          );
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba($bs-success, 0.3);
        }

        &:disabled {
          background: $bs-secondary;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        i {
          margin-right: 0.5rem;
        }
      }
    }
  }
}

// Modal styles
.modal-content {
  border-radius: 1.5rem;
  border: none;
  box-shadow: 0 20px 40px rgba($bs-dark, 0.15);

  .modal-header {
    border-bottom: 1px solid rgba($bs-primary, 0.1);
    padding: 1.5rem;

    .modal-title {
      font-family: $font-heading !important;
      color: #1e40af !important;
      font-weight: 700;
    }
  }

  .modal-body {
    padding: 2rem;

    .exchange-item-preview {
      h4 {
        font-family: $font-heading !important;
        color: #1e40af !important;
      }

      p {
        font-family: $primary-font !important;
        color: $bs-secondary;
      }
    }

    .cost-display {
      background: rgba($bs-warning, 0.1);
      padding: 1rem 2rem;
      border-radius: 1rem;
      border: 2px solid rgba($bs-warning, 0.2);

      .fs-4 {
        color: $bs-warning;
        font-family: $font-heading !important;
        font-weight: 700;
      }
    }

    .points-check {
      font-family: $primary-font !important;
      color: $bs-dark;
    }
  }

  .modal-footer {
    border-top: 1px solid rgba($bs-primary, 0.1);
    padding: 1.5rem;

    .btn {
      border-radius: 0.75rem;
      padding: 0.75rem 1.5rem;
      font-family: $primary-font !important;
      font-weight: 600;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .rewards-header {
    padding: 2rem 0;

    .section-title {
      font-size: 2rem;
    }

    .lead {
      font-size: 1rem;
    }
  }

  .points-badge {
    padding: 0.625rem 1.25rem;
    font-size: 0.9rem;

    .points-amount {
      font-size: 1.1rem;
    }

    .points-unit {
      font-size: 0.8rem;
    }
  }

  .category-filters {
    .btn-category {
      padding: 0.625rem 1.25rem;
      font-size: 0.9rem;
    }
  }

  .reward-item {
    .reward-image {
      height: 200px;
    }

    .reward-content {
      padding: 1.25rem;

      .reward-title {
        font-size: 1.2rem;
      }
    }
  }
}
