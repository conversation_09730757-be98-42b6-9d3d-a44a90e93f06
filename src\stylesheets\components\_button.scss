@use "../abstracts/" as *;

.btn-cta {
  background: linear-gradient(135deg, #b3ff74 0%, #9ae65c 100%) !important;
  color: #2d3748;
  border: none;
  font-weight: 600;
  font-size: 1.14rem;
  padding: 13px 36px;
  border-radius: 30px;
  box-shadow: 0 4px 14px rgba(#b3ff74, 0.3);
  transition: all 0.3s ease;
}

.btn-cta:hover {
  background: linear-gradient(135deg, #eeffdd 0%, #d4f1c5 100%) !important;
  color: #2d3748;
  box-shadow: 0 8px 26px rgba(#eeffdd, 0.4);
  transform: translateY(-2px);
}

// Responsive button styling
@media (max-width: 768px) {
  .btn-cta {
    font-size: 1rem;
    padding: 12px 28px;
    border-radius: 25px;
    box-shadow: 0 3px 12px rgba(#b3ff74, 0.25);
  }

  .btn-cta:hover {
    box-shadow: 0 6px 20px rgba(#eeffdd, 0.35);
    transform: translateY(-1px);
  }
}

@media (max-width: 576px) {
  .btn-cta {
    font-size: 0.95rem;
    padding: 11px 24px;
    border-radius: 22px;
    white-space: nowrap;
    max-width: 90%;
    display: inline-block;
    text-align: center;
  }

  .btn-cta:hover {
    transform: translateY(-1px);
  }
}

@media (max-width: 360px) {
  .btn-cta {
    font-size: 0.9rem;
    padding: 10px 20px;
    border-radius: 20px;
    max-width: 85%;
  }
}
