{"name": "recyclecharity-backend", "version": "1.0.0", "description": "Backend API for RecycleCharity website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "create-admin": "node scripts/createAdmin.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "mongodb", "authentication"], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2"}}