@use "../../abstracts/" as *;
@use "sass:color";

// Contact page header section
.container.py-5 {
  .section-title {
    font-family: $font-heading !important;
    color: #1e40af !important; // Consistent with homepage titles
    font-weight: 700;
    letter-spacing: $letter-spacing-tight;
  }

  .lead.text-muted {
    font-family: $primary-font !important;
    color: $bs-secondary !important;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.contact-card {
  border: none;
  background: linear-gradient(145deg, $bs-light, rgba($bs-light, 0.8));
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba($bs-dark, 0.1);
  transition: all 0.4s ease;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba($bs-primary, 0.1);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, $bs-primary, $primary-color);
    border-radius: 1.5rem 1.5rem 0 0;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba($bs-primary, 0.15);
  }

  .card-body {
    padding: 2rem 1.75rem 1.75rem; // Reduced padding

    .card-title {
      font-family: $font-heading !important;
      font-size: 1.6rem; // Slightly smaller
      color: #1e40af !important; // Consistent with homepage titles
      margin-bottom: 1.25rem; // Reduced margin
      font-weight: 700;
      letter-spacing: $letter-spacing-tight;

      i {
        color: $bs-primary;
        margin-right: 0.5rem;
      }
    }
  }
}

#contactForm {
  .form-label {
    font-family: $primary-font !important;
    font-weight: 600;
    color: $bs-dark;
    font-size: 1rem;
  }

  .form-control {
    border-radius: 0.75rem;
    border: 2px solid rgba($bs-primary, 0.1);
    transition: all 0.3s ease;
    font-family: $primary-font !important;
    padding: 0.625rem 0.875rem; // Reduced padding
    font-size: 0.95rem; // Slightly smaller font

    &:focus {
      border-color: $bs-primary;
      box-shadow: 0 0 0 0.25rem rgba($bs-primary, 0.1);
      outline: none;
      transform: translateY(-2px);
    }

    &::placeholder {
      color: $bs-secondary;
      opacity: 0.7;
    }
  }

  textarea.form-control {
    resize: vertical;
    min-height: 100px; // Reduced height
  }

  .btn-success {
    background: linear-gradient(135deg, $bs-success, $light-green);
    border: none;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    font-family: $primary-font !important;
    font-weight: 600;
    padding: 0.75rem 1.75rem; // Reduced padding
    font-size: 1rem; // Smaller font size

    &:hover {
      background: linear-gradient(
        135deg,
        color.adjust($bs-success, $lightness: -10%),
        $bs-success
      );
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba($bs-success, 0.3);
    }

    &:focus {
      box-shadow: 0 0 0 0.25rem rgba($bs-success, 0.25);
    }

    i {
      margin-right: 0.5rem;
    }
  }
}

#formFeedback {
  font-family: $primary-font !important;
  font-size: 0.9rem;
  font-weight: 500;
}

.contact-info-card {
  background: linear-gradient(145deg, $bs-light, rgba($bs-light, 0.8));
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba($bs-dark, 0.1);
  transition: all 0.4s ease;
  position: relative;
  border: 1px solid rgba($bs-primary, 0.1);
  margin-bottom: 2rem;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, $bs-info, $accent-cyan);
    border-radius: 1.5rem 1.5rem 0 0;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba($bs-primary, 0.15);
  }

  .section-title-sm {
    font-family: $font-heading !important;
    font-size: 1.6rem;
    color: #1e40af !important; // Consistent with homepage titles
    margin-bottom: 1.5rem;
    font-weight: 700;
    letter-spacing: $letter-spacing-tight;

    i {
      margin-right: 0.5rem;
      color: $bs-info;
    }
  }
}

.contact-info-list {
  padding-left: 0;

  li {
    display: flex;
    align-items: flex-start;
    font-family: $primary-font !important;
    font-size: 1rem;
    color: $bs-dark;
    line-height: 1.6;

    &:not(:last-child) {
      margin-bottom: 1.25rem;
    }

    i {
      font-size: 1.3rem;
      width: 35px;
      flex-shrink: 0;
      color: $bs-primary;
      margin-top: 0.1rem;
    }

    strong {
      margin-right: 0.5rem;
      font-weight: 600;
    }

    a {
      color: $bs-dark;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        color: $bs-primary;
        text-decoration: underline;
      }
    }
  }
}

.social-icons-contact {
  .btn {
    width: 55px;
    height: 55px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 1.4rem;
    transition: all 0.3s ease;
    border-width: 2px;
    font-weight: 600;

    &:hover {
      transform: translateY(-3px) scale(1.1);
      box-shadow: 0 8px 25px rgba($bs-primary, 0.2);
    }

    &.btn-outline-primary:hover {
      background: $bs-primary;
      border-color: $bs-primary;
    }

    &.btn-outline-danger:hover {
      background: $bs-danger;
      border-color: $bs-danger;
    }

    &.btn-outline-dark:hover {
      background: $bs-dark;
      border-color: $bs-dark;
    }

    &.btn-outline-info:hover {
      background: $bs-info;
      border-color: $bs-info;
    }
  }
}

#contact-map-placeholder {
  min-height: 200px;
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: $bs-light;
  border: 2px solid rgba($bs-primary, 0.1);
  border-radius: 1.5rem;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba($bs-primary, 0.2);
    box-shadow: 0 8px 25px rgba($bs-primary, 0.1);
  }

  iframe {
    border-radius: 1.5rem;
  }

  p {
    font-family: $primary-font !important;
    font-size: 0.95rem;
    color: $bs-secondary;
  }
}

// Responsive Adjustments
@media (max-width: 768px) {
  .container.py-5 {
    .section-title {
      font-size: 2rem;
    }

    .lead.text-muted {
      font-size: 1rem;
    }
  }

  .contact-card {
    .card-body {
      padding: 2rem 1.5rem 1.5rem;

      .card-title {
        font-size: 1.5rem;
      }
    }
  }

  #contactForm {
    .form-label {
      font-size: 0.9rem;
    }

    .form-control {
      padding: 0.625rem 0.875rem;
    }

    .btn-success {
      font-size: 1rem;
      padding: 0.75rem 1.5rem;
    }
  }

  .contact-info-card {
    padding: 1.5rem;
    margin-bottom: 1.5rem;

    .section-title-sm {
      font-size: 1.4rem;
    }
  }

  .contact-info-list li {
    font-size: 0.9rem;

    i {
      font-size: 1.2rem;
      width: 30px;
    }
  }

  .social-icons-contact .btn {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }

  #contact-map-placeholder {
    height: 200px;
  }
}
